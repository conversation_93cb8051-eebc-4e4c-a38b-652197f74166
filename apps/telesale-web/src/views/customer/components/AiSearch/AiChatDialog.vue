<!--
 * @Date         : 2025-07-14 17:00:00
 * @Description  : AI对话悬浮组件 - 自定义样式，无遮罩层
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { ref, nextTick, computed } from "vue";
import { ElMessage } from "element-plus";
import { mockAiSearchResponse } from "/@/api/customer/aiSearch";
import point from "./utils/point";

interface Message {
  id: string;
  content: string;
  isBot: boolean;
  timestamp: number;
  isConfirmation?: boolean;
  isLoading?: boolean;
  confirmationData?: {
    filters: string;
    timeRange: string;
    intention: string;
  };
  data?: any;
  relatedUserMessage?: string; // 关联的用户消息内容
}

interface Props {
  visible: boolean;
}

interface Emits {
  (e: "update:visible", value: boolean): void;
  (e: "confirm", val: any): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const isVisible = computed({
  get: () => props.visible,
  set: value => emit("update:visible", value)
});

const messages = ref<Message[]>([
  {
    id: "1",
    content:
      "您好！我是AI助手，请告诉我您想要查询的内容，我会帮您分析并提供筛选条件。",
    isBot: true,
    timestamp: Date.now()
  }
]);

const inputMessage = ref("");
const chatContentRef = ref<HTMLElement>();
const loading = ref(false);
const sessionId = ref("");
const lastUserMessage = ref("");
const data = ref();

// 添加消息
const addMessage = (
  content: string,
  isBot: boolean,
  isConfirmation = false,
  confirmationData?: any,
  data?: any,
  relatedUserMessage?: string
) => {
  const message: Message = {
    id: Date.now().toString(),
    content,
    isBot,
    timestamp: Date.now(),
    isConfirmation,
    confirmationData,
    data,
    relatedUserMessage
  };
  messages.value.push(message);
  scrollToBottom();
};

// 发送消息
const sendMessage = async () => {
  if (!inputMessage.value.trim() || loading.value) return;
  point.h5Post("clickCRMCustomerAISearch", {
    from: "客户池",
    button: "AISearchSend"
  });
  const userMessage = inputMessage.value.trim();
  lastUserMessage.value = userMessage;
  addMessage(userMessage, false);

  // 清空输入框
  inputMessage.value = "";

  // 显示加载状态
  loading.value = true;

  // 添加loading消息
  const loadingMessageId = Date.now().toString() + "_loading";
  const loadingMessage = {
    id: loadingMessageId,
    content: "正在思考中",
    isBot: true,
    timestamp: Date.now(),
    isLoading: true
  };
  messages.value.push(loadingMessage);
  scrollToBottom();

  try {
    // 调用AI搜索API（目前使用模拟API）
    const response = await mockAiSearchResponse(userMessage);

    // 移除loading消息
    const loadingIndex = messages.value.findIndex(
      msg => msg.id === loadingMessageId
    );
    if (loadingIndex !== -1) {
      messages.value.splice(loadingIndex, 1);
    }

    // 更新会话ID
    if (response.sessionId) {
      sessionId.value = response.sessionId;
    }

    // 添加AI回复，关联到用户消息
    addMessage(
      response.message,
      true,
      response.isConfirmation,
      response.confirmationData,
      response.data,
      userMessage // 关联用户消息
    );
  } catch (error) {
    console.error("发送消息失败:", error);
    ElMessage.error("发送消息失败，请重试");

    // 移除loading消息
    const loadingIndex = messages.value.findIndex(
      msg => msg.id === loadingMessageId
    );
    if (loadingIndex !== -1) {
      messages.value.splice(loadingIndex, 1);
    }

    addMessage("抱歉，我遇到了一些问题，请稍后重试。", true);
  } finally {
    loading.value = false;
  }
};

// 重新解析
const handleReparse = async (messageId: string) => {
  // 找到对应的AI消息
  const aiMessage = messages.value.find(msg => msg.id === messageId);
  if (!aiMessage || !aiMessage.relatedUserMessage) {
    ElMessage.error("无法找到对应的用户消息");
    return;
  }

  const userMessageContent = aiMessage.relatedUserMessage;

  point.h5Post("clickCRMCustomerAISearch", {
    from: "客户池",
    button: "AISearchReSend"
  });
  loading.value = true;

  // 添加loading消息
  const loadingMessageId = Date.now().toString() + "_reparse_loading";

  try {
    // 重新发送用户的原始消息
    addMessage(userMessageContent, false);
    const loadingMessage = {
      id: loadingMessageId,
      content: "正在重新分析",
      isBot: true,
      timestamp: Date.now(),
      isLoading: true
    };
    messages.value.push(loadingMessage);
    scrollToBottom();

    // 调用重新解析API
    const response = await mockAiSearchResponse(userMessageContent);

    // 移除loading消息
    const loadingIndex = messages.value.findIndex(
      msg => msg.id === loadingMessageId
    );
    if (loadingIndex !== -1) {
      messages.value.splice(loadingIndex, 1);
    }

    // 添加重新解析的结果，并关联用户消息
    const newAiMessage = {
      id: Date.now().toString(),
      content: response.message,
      isBot: true,
      timestamp: Date.now(),
      isConfirmation: response.isConfirmation,
      confirmationData: response.confirmationData,
      data: response,
      relatedUserMessage: userMessageContent // 关联用户消息
    };

    messages.value.push(newAiMessage);
    scrollToBottom();
  } catch (error) {
    console.error("重新解析失败:", error);
    ElMessage.error("重新解析失败，请重试");

    // 移除loading消息
    const loadingIndex = messages.value.findIndex(
      msg => msg.id === loadingMessageId
    );
    if (loadingIndex !== -1) {
      messages.value.splice(loadingIndex, 1);
    }

    addMessage("重新解析失败，请稍后重试。", true);
  } finally {
    loading.value = false;
  }
};

// 确认筛选条件
const handleConfirm = async (data: any) => {
  emit("confirm", data);
  isVisible.value = false;
};

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (chatContentRef.value) {
      chatContentRef.value.scrollTop = chatContentRef.value.scrollHeight;
    }
  });
};

// 关闭对话框
const handleClose = () => {
  isVisible.value = false;
};

// 键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === "Enter" && !event.shiftKey) {
    event.preventDefault();
    sendMessage();
  }
};
</script>

<template>
  <!-- 悬浮对话框 - 无遮罩层 -->
  <div v-if="isVisible" class="ai-chat-floating">
    <!-- 对话框头部 -->
    <div class="chat-header">
      <div class="header-title">
        <span class="title-text">AI搜索</span>
      </div>
      <button class="close-button" @click="handleClose">×</button>
    </div>

    <!-- 对话内容区域 -->
    <div ref="chatContentRef" class="chat-content">
      <div class="message-list">
        <div
          v-for="message in messages"
          :key="message.id"
          :class="[
            'message-item',
            message.isBot ? 'bot-message' : 'user-message'
          ]"
        >
          <!-- AI消息 -->
          <div v-if="message.isBot" class="bot-message-wrapper">
            <div class="bot-avatar">
              <img src="/@/assets/favicon.ico" alt="AI" />
            </div>
            <div
              :class="[
                'message-bubble',
                'bot-bubble',
                { 'loading-message': message.isLoading }
              ]"
            >
              <div class="message-content">
                {{ message.content }}
                <!-- 加载动画 -->
                <span v-if="message.isLoading" class="loading-dots">
                  <span class="dot" />
                  <span class="dot" />
                  <span class="dot" />
                </span>
              </div>

              <!-- 确认按钮区域 -->
              <div v-if="message.isConfirmation" class="confirmation-buttons">
                <button
                  class="action-button secondary"
                  @click="() => handleReparse(message.id)"
                  :disabled="loading"
                >
                  重新解析
                </button>
                <button
                  class="action-button primary"
                  @click="handleConfirm(message.data)"
                  :disabled="loading"
                >
                  确认
                </button>
              </div>
            </div>
          </div>

          <!-- 用户消息 -->
          <div v-else class="user-message-wrapper">
            <div class="message-bubble user-bubble">
              <div class="message-content">{{ message.content }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="input-area">
      <div class="input-wrapper">
        <textarea
          v-model="inputMessage"
          placeholder="请输入您想查询的内容"
          class="message-input"
          rows="1"
          @keydown="handleKeydown"
          :disabled="loading"
        />
        <button
          class="send-button"
          @click="sendMessage"
          :disabled="!inputMessage.trim() || loading"
        >
          <svg
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M2 21L23 12L2 3V10L17 12L2 14V21Z" fill="currentColor" />
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.ai-chat-floating {
  position: fixed;
  top: 154px;
  right: 44px;
  width: 420px;
  height: 520px;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  border: 1px solid #e1e5e9;
  z-index: 2000;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  // 头部样式
  .chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 18px 24px;
    border-bottom: 1px solid #f0f0f0;
    background: #ffffff;

    .header-title {
      .title-text {
        font-size: 17px;
        font-weight: 600;
        color: #1d1d1f;
      }
    }

    .close-button {
      width: 28px;
      height: 28px;
      border: none;
      background: none;
      font-size: 20px;
      color: #8e8e93;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 6px;
      transition: all 0.2s ease;

      &:hover {
        background: #f2f2f7;
        color: #48484a;
      }
    }
  }

  // 对话内容区域
  .chat-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px 16px;
    background-color: #ffffff;

    .message-list {
      display: flex;
      flex-direction: column;
      gap: 16px;
      padding: 4px 0;
    }

    .message-item {
      display: flex;
      width: 100%;
    }

    // AI消息布局 - 左侧
    .bot-message-wrapper {
      display: flex;
      align-items: flex-start;
      gap: 10px;
      justify-content: flex-start;
      width: 100%;
    }

    // 用户消息布局 - 右侧
    .user-message-wrapper {
      display: flex;
      justify-content: flex-end;
      width: 100%;
    }

    // AI头像
    .bot-avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      overflow: hidden;
      flex-shrink: 0;
      margin-top: 2px;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    // 消息气泡基础样式
    .message-bubble {
      padding: 12px 16px;
      border-radius: 16px;
      word-wrap: break-word;
      line-height: 1.5;
      font-size: 14px;
      position: relative;
    }

    // AI消息气泡
    .bot-bubble {
      background-color: #f0f0f0;
      color: #333;
      border-bottom-left-radius: 6px;
      max-width: calc(100% - 42px); // 减去头像宽度和间距
      margin-left: 0;
    }

    // 用户消息气泡
    .user-bubble {
      background-color: #007aff;
      color: white;
      border-bottom-right-radius: 6px;
      max-width: calc(100% - 42px); // 保持与AI消息相同的可用宽度
      margin-right: 0;
      text-align: left; // 文本左对齐，但气泡整体右对齐
    }

    .message-content {
      white-space: pre-wrap;
      text-align: inherit;
    }

    // Loading消息样式
    .loading-message {
      background-color: #f8f9fa !important;
      border: 1px solid #e9ecef;
      opacity: 0.9;
    }

    // Loading动画
    .loading-dots {
      display: inline-flex;
      align-items: center;
      margin-left: 8px;

      .dot {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background-color: #6c757d;
        margin: 0 2px;
        animation: loading-bounce 1.4s infinite ease-in-out both;

        &:nth-child(1) {
          animation-delay: -0.32s;
        }

        &:nth-child(2) {
          animation-delay: -0.16s;
        }

        &:nth-child(3) {
          animation-delay: 0s;
        }
      }
    }

    @keyframes loading-bounce {
      0%,
      80%,
      100% {
        transform: scale(0.6);
        opacity: 0.3;
      }
      40% {
        transform: scale(1);
        opacity: 1;
      }
    }

    .confirmation-buttons {
      margin-top: 12px;
      display: flex;
      gap: 10px;
      justify-content: flex-start;

      .action-button {
        padding: 8px 16px;
        border: none;
        border-radius: 8px;
        font-size: 13px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        min-width: 70px;

        &.primary {
          background: #007aff;
          color: white;

          &:hover:not(:disabled) {
            background: #0056cc;
            transform: translateY(-1px);
          }
        }

        &.secondary {
          background: #f8f9fa;
          color: #6c757d;
          border: 1px solid #dee2e6;

          &:hover:not(:disabled) {
            background: #e9ecef;
            color: #495057;
            transform: translateY(-1px);
          }
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none !important;
        }
      }
    }
  }

  // 输入区域
  .input-area {
    padding: 16px 20px;
    border-top: 1px solid #f0f0f0;
    background: #ffffff;

    .input-wrapper {
      display: flex;
      gap: 12px;
      align-items: flex-end;

      .message-input {
        flex: 1;
        min-height: 40px;
        max-height: 100px;
        padding: 12px 16px;
        border: 1px solid #e1e5e9;
        border-radius: 12px;
        font-size: 14px;
        line-height: 1.5;
        resize: none;
        outline: none;
        transition: all 0.2s ease;
        background: #f8f9fa;

        &:focus {
          border-color: #007aff;
          background: #ffffff;
          box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }

        &::placeholder {
          color: #8e8e93;
        }

        &:disabled {
          background: #f2f2f7;
          color: #8e8e93;
        }
      }

      .send-button {
        width: 40px;
        height: 40px;
        background: #007aff;
        border: none;
        border-radius: 12px;
        color: white;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;

        &:hover:not(:disabled) {
          background: #0056cc;
          transform: translateY(-1px);
        }

        &:disabled {
          background: #c7c7cc;
          cursor: not-allowed;
          transform: none;
        }
      }
    }
  }

  // 滚动条样式
  .chat-content::-webkit-scrollbar {
    width: 6px;
  }

  .chat-content::-webkit-scrollbar-track {
    background: transparent;
  }

  .chat-content::-webkit-scrollbar-thumb {
    background: #d1d1d6;
    border-radius: 3px;
  }

  .chat-content::-webkit-scrollbar-thumb:hover {
    background: #c7c7cc;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .ai-chat-floating {
    width: 380px;
    height: 480px;
    top: 60px;
    right: 15px;
  }
}

@media (max-width: 480px) {
  .ai-chat-floating {
    width: calc(100vw - 24px);
    height: 420px;
    top: 40px;
    right: 12px;
    left: 12px;
  }
}
</style>
