/*
 * @Date         : 2025-07-15 16:51:51
 * @Description  : 客户池埋点
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { BuryPoint, envType } from "@guanghe-pub/onion-utils";
import axios from "axios";
import { storeToRefs } from "pinia";
import { useUserStore } from "/@/store/modules/user";
import dayjs from "dayjs";

const { userMsg, allAgentObj } = storeToRefs(useUserStore());

const env = import.meta.env.VITE_POINT as envType;

// button: AISearch;AISearchSend;AutoSearch;ManualSearch;
const points = {
  clickCRMCustomerAISearch: {
    category: "site",
    desc: "点击CRM后台客户池AI搜索按钮",
    data: ["u_user", "userId", "from", "applyTimeBegin", "button"]
  }
};

export default new BuryPoint(points, {
  contextData: {
    productId: "38",
    u_user: userMsg.value.name,
    userId: userMsg.value.id + "",
    applyTimeBegin: dayjs().unix()
  },
  env,
  axios
});
